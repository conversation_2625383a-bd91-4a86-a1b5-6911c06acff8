<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend Authentication Flow</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Frontend Authentication Flow Test</h1>
    
    <div class="step info">
        <h3>Test Steps:</h3>
        <ol>
            <li>Login to NestJS and get access_token</li>
            <li>Simulate frontend localStorage setup</li>
            <li>Test Python backend API calls with Bearer token</li>
            <li>Verify all endpoints work correctly</li>
        </ol>
    </div>

    <button onclick="runFullTest()">🚀 Run Full Test</button>
    <button onclick="clearResults()">🧹 Clear Results</button>

    <div id="results"></div>

    <script>
        let testResults = [];

        function addResult(step, status, message, data = null) {
            const result = { step, status, message, data, timestamp: new Date().toISOString() };
            testResults.push(result);
            updateDisplay();
        }

        function updateDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = testResults.map(result => `
                <div class="step ${result.status}">
                    <h4>${result.step}</h4>
                    <p><strong>Status:</strong> ${result.status.toUpperCase()}</p>
                    <p><strong>Message:</strong> ${result.message}</p>
                    ${result.data ? `<pre>${JSON.stringify(result.data, null, 2)}</pre>` : ''}
                    <small>Time: ${result.timestamp}</small>
                </div>
            `).join('');
        }

        function clearResults() {
            testResults = [];
            updateDisplay();
        }

        async function runFullTest() {
            clearResults();
            addResult('🏁 Starting Test', 'info', 'Beginning full authentication flow test...');

            try {
                // Step 1: Login to NestJS
                addResult('1️⃣ NestJS Login', 'info', 'Attempting login to NestJS backend...');
                
                const loginResponse = await fetch('http://localhost:3000/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                if (!loginResponse.ok) {
                    throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`);
                }

                const loginData = await loginResponse.json();
                const accessToken = loginData.access_token;

                if (!accessToken) {
                    throw new Error('No access_token in login response');
                }

                addResult('1️⃣ NestJS Login', 'success', 'Login successful!', {
                    user: loginData.user,
                    tokenPreview: accessToken.substring(0, 30) + '...'
                });

                // Step 2: Simulate frontend localStorage setup
                addResult('2️⃣ Frontend Setup', 'info', 'Simulating frontend localStorage and API setup...');
                
                localStorage.setItem('user', JSON.stringify(loginData.user));
                localStorage.setItem('isAuthenticated', 'true');
                localStorage.setItem('access_token', accessToken);

                // Setup axios with token (simulating frontend API service)
                axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;

                addResult('2️⃣ Frontend Setup', 'success', 'Frontend state simulated successfully', {
                    localStorage: {
                        user: !!localStorage.getItem('user'),
                        isAuthenticated: localStorage.getItem('isAuthenticated'),
                        access_token: !!localStorage.getItem('access_token')
                    },
                    axiosHeaders: axios.defaults.headers.common
                });

                // Step 3: Test Python backend APIs
                addResult('3️⃣ Python Backend APIs', 'info', 'Testing Python backend endpoints...');

                const endpoints = [
                    { name: 'Auth Me', url: 'http://127.0.0.1:8000/api/auth/me' },
                    { name: 'Profiles', url: 'http://127.0.0.1:8000/api/profiles/' },
                    { name: 'Scraping', url: 'http://127.0.0.1:8000/api/scraping/' },
                    { name: 'Exports History', url: 'http://127.0.0.1:8000/api/scraping/exports/history' }
                ];

                const apiResults = {};
                for (const endpoint of endpoints) {
                    try {
                        const response = await axios.get(endpoint.url);
                        apiResults[endpoint.name] = {
                            status: response.status,
                            dataLength: Array.isArray(response.data) ? response.data.length : 'N/A',
                            success: true
                        };
                    } catch (error) {
                        apiResults[endpoint.name] = {
                            status: error.response?.status || 'ERROR',
                            error: error.message,
                            success: false
                        };
                    }
                }

                const allSuccess = Object.values(apiResults).every(result => result.success);
                
                addResult('3️⃣ Python Backend APIs', allSuccess ? 'success' : 'error', 
                    allSuccess ? 'All API endpoints working!' : 'Some API endpoints failed', 
                    apiResults);

                // Step 4: Final summary
                if (allSuccess) {
                    addResult('🎉 Test Complete', 'success', 'All tests passed! Authentication flow is working correctly.');
                } else {
                    addResult('❌ Test Failed', 'error', 'Some tests failed. Check the results above.');
                }

            } catch (error) {
                addResult('❌ Test Error', 'error', `Test failed: ${error.message}`, {
                    error: error.toString(),
                    stack: error.stack
                });
            }
        }
    </script>
</body>
</html>
