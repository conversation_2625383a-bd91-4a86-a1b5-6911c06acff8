<!DOCTYPE html>
<html>
<head>
    <title>Debug Login Flow</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .info { background-color: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Login Flow</h1>
        
        <div class="step">
            <h3>Step 1: Login</h3>
            <button onclick="testLogin()"><NAME_EMAIL></button>
            <div id="loginResult"></div>
        </div>

        <div class="step">
            <h3>Step 2: Check localStorage</h3>
            <button onclick="checkLocalStorage()">Check localStorage</button>
            <div id="storageResult"></div>
        </div>

        <div class="step">
            <h3>Step 3: Test API Call</h3>
            <button onclick="testApiCall()">Test Python API</button>
            <div id="apiResult"></div>
        </div>

        <div class="step">
            <h3>Step 4: Debug Headers</h3>
            <button onclick="debugHeaders()">Debug Request Headers</button>
            <div id="headersResult"></div>
        </div>
    </div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<p>Logging in...</p>';

            try {
                const response = await fetch('http://localhost:3000/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                if (!response.ok) {
                    throw new Error(`Login failed: ${response.status}`);
                }

                const data = await response.json();
                console.log('Login response:', data);

                // Store in localStorage (simulating frontend)
                localStorage.setItem('user', JSON.stringify(data.user));
                localStorage.setItem('isAuthenticated', 'true');
                localStorage.setItem('access_token', data.access_token);

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Login Successful</h4>
                        <p><strong>User:</strong> ${data.user.email} (${data.user.role})</p>
                        <p><strong>Token:</strong> ${data.access_token.substring(0, 30)}...</p>
                    </div>
                `;
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Login Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function checkLocalStorage() {
            const resultDiv = document.getElementById('storageResult');
            
            const user = localStorage.getItem('user');
            const isAuthenticated = localStorage.getItem('isAuthenticated');
            const accessToken = localStorage.getItem('access_token');

            resultDiv.innerHTML = `
                <div class="info">
                    <h4>📋 localStorage Contents</h4>
                    <p><strong>user:</strong> ${user ? 'Present' : 'Missing'}</p>
                    <p><strong>isAuthenticated:</strong> ${isAuthenticated}</p>
                    <p><strong>access_token:</strong> ${accessToken ? accessToken.substring(0, 30) + '...' : 'Missing'}</p>
                    <pre>${JSON.stringify({
                        user: user ? JSON.parse(user) : null,
                        isAuthenticated,
                        tokenLength: accessToken ? accessToken.length : 0
                    }, null, 2)}</pre>
                </div>
            `;
        }

        async function testApiCall() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>Testing API call...</p>';

            const accessToken = localStorage.getItem('access_token');
            if (!accessToken) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ No Token</h4>
                        <p>Please login first to get access token</p>
                    </div>
                `;
                return;
            }

            try {
                const response = await fetch('http://127.0.0.1:8000/api/profiles/', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                console.log('API response:', data);

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ API Call Successful</h4>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Data:</strong> ${Array.isArray(data) ? data.length + ' items' : 'Object'}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('API error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ API Call Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function debugHeaders() {
            const resultDiv = document.getElementById('headersResult');
            const accessToken = localStorage.getItem('access_token');

            const headers = {
                'Authorization': accessToken ? `Bearer ${accessToken}` : 'NOT SET',
                'Content-Type': 'application/json'
            };

            resultDiv.innerHTML = `
                <div class="info">
                    <h4>🔍 Request Headers Debug</h4>
                    <pre>${JSON.stringify(headers, null, 2)}</pre>
                    <p><strong>Token Available:</strong> ${!!accessToken}</p>
                    <p><strong>Token Length:</strong> ${accessToken ? accessToken.length : 0}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
