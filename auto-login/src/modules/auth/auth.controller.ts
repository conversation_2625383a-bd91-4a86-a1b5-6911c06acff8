import {
  Controller,
  Post,
  Body,
  Get,
  Render,
  UseGuards,
  Req,
  Query,
  Res,
  Param,
  UnauthorizedException,
  HttpException,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../users/users.service';
import { JwtAuthGuard } from './jwt-auth.guard';
import { RolesGuard } from './roles.guard';
import { Roles } from './roles.decorator';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
  ) {}

  @Get('login')
  @Render('auth/login')
  showLogin() {
    return {};
  }

  @Post('register')
  register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto, @Res() res) {
    const { access_token, refresh_token } =
      await this.authService.login(loginDto);

    // Get user data from token
    const decoded = await this.authService.verifyToken(access_token);
    const user = await this.usersService.findById(decoded.id);

    // Set HTTP-only cookies
    // Access token cookie - short lived
    res.cookie('access_token', access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 3600000, // 1 hour
      // Remove domain to allow sharing between ports
    });

    // Refresh token cookie - longer lived
    res.cookie('refresh_token', refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 7 * 24 * 3600000, // 7 days
      // Remove path restriction to allow access from all endpoints
      // Remove domain to allow sharing between ports
    });

    return res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        status: user.status,
      },
      access_token: access_token // Include token for frontend API calls
    });
  }

  @Post('auth/refresh')
  async refreshToken(@Req() req, @Res() res) {
    try {
      const refreshToken = req.cookies['refresh_token'];

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not found');
      }

      const { access_token, refresh_token } =
        await this.authService.refreshToken(refreshToken);

      // Set new HTTP-only cookies
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 3600000, // 1 hour
        // Remove domain to allow sharing between ports
      });

      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        // Remove path restriction to allow access from all endpoints
        // Remove domain to allow sharing between ports
      });

      return res.json({ success: true });
    } catch (error) {
      // Clear cookies on error with same options as when setting them
      res.clearCookie('access_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        // Remove domain to match cookie setting
      });

      res.clearCookie('refresh_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        // Remove path restriction to match cookie setting
        // Remove domain to match cookie setting
      });

      return res.status(401).json({
        success: false,
        message: error.message || 'Invalid refresh token',
      });
    }
  }

  @Get('auth/status')
  async getAuthStatus(@Req() req, @Res() res) {
    try {
      const token = req.cookies['access_token'];
      if (!token) {
        return res.json({
          isAuthenticated: false,
          user: null,
          status: null,
        });
      }

      const decoded = await this.authService.verifyToken(token);
      return res.json({
        isAuthenticated: true,
        user: decoded,
      });
    } catch (error) {
      return res.json({
        isAuthenticated: false,
        user: null,
        status: null,
      });
    }
  }

  @Get('auth/google')
  async googleAuth(
    @Query('return_url') returnUrl: string,
    @Query('session_id') sessionId: string,
    @Req() req: any,
    @Res() res: any,
  ) {
    console.log('🔍 [AUTH] Google OAuth initiated with session ID:', sessionId);

    // Store session ID in global map for callback
    if (sessionId) {
      if (!global.pendingSessions) {
        global.pendingSessions = new Map();
      }
      global.pendingSessions.set(sessionId, sessionId);
      console.log('🔍 [AUTH] Stored session ID in global map:', sessionId);
    }

    // Manually construct Google OAuth URL with state parameter
    const googleClientId = process.env.GOOGLE_CLIENT_ID;
    const googleCallbackUrl = process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3000/auth/google/callback';
    const state = sessionId ? `session_id=${sessionId}` : '';

    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${googleClientId}&` +
      `redirect_uri=${encodeURIComponent(googleCallbackUrl)}&` +
      `response_type=code&` +
      `scope=email%20profile&` +
      `state=${encodeURIComponent(state)}`;

    console.log('🔍 [AUTH] Redirecting to Google OAuth URL:', googleAuthUrl);
    return res.redirect(googleAuthUrl);
  }

  @Get('auth/discord')
  @UseGuards(AuthGuard('discord'))
  async discordAuth(@Query('return_url') returnUrl?: string) {
    // Store return URL in session for callback
    if (returnUrl) {
      // You might want to validate the return URL here
      return { returnUrl };
    }
  }

  @Post('logout')
  async logout(@Res() res) {
    // Clear both cookies with same options as when setting them
    res.clearCookie('access_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      // Remove domain to match cookie setting
    });

    res.clearCookie('refresh_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      // Remove path restriction to match cookie setting
      // Remove domain to match cookie setting
    });

    return res.json({ success: true });
  }

  @Get('auth/google/callback')
  @UseGuards(AuthGuard(['google', 'google-token']))
  async googleAuthCallback(@Req() req, @Res() res) {
    try {
      console.log('🔍 [AUTH] Google OAuth callback received');
      console.log('🔍 [AUTH] Query params:', req.query);
      console.log('🔍 [AUTH] User data:', req.user);
      console.log('🔍 [AUTH] Request headers:', req.headers);

      // Check if this is a token-based flow by checking the referrer or state
      const isTokenFlow = req.query.state?.includes('token=true') ||
                         req.headers.referer?.includes('/auth/token/google') ||
                         req.query.token === 'true';

      console.log('🔍 [AUTH] Is token flow:', isTokenFlow);

      const { access_token, refresh_token } =
        await this.authService.validateGoogleLogin(req.user);

      console.log('🔍 [AUTH] Tokens generated successfully');

      // Decode the access token to get full user data
      const decodedUser = await this.authService.verifyToken(access_token);
      console.log('🔍 [AUTH] Decoded user data:', decodedUser);

      // Store recent login for Electron app to detect
      if (!global.recentLogins) {
        global.recentLogins = [];
      }

      global.recentLogins.push({
        email: decodedUser.email,
        user: decodedUser, // Use decoded user data with full info (id, email, role, status)
        access_token,
        refresh_token,
        timestamp: Date.now()
      });

      // Clean up old logins (older than 5 minutes) or used logins (older than 30 seconds)
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      const thirtySecondsAgo = Date.now() - 30 * 1000;
      global.recentLogins = global.recentLogins.filter(login => {
        // Keep if not too old
        if (login.timestamp <= fiveMinutesAgo) return false;
        // Keep if used but not too long ago
        if (login.used && login.usedAt <= thirtySecondsAgo) return false;
        return true;
      });

      console.log('✅ [AUTH] Recent login stored for Electron detection');

      // Handle token-based flow - store tokens and redirect to success page
      if (isTokenFlow) {
        console.log('🔍 [TOKEN_AUTH] Handling token-based OAuth callback');

        // Store tokens in global variable for Electron app to retrieve
        if (!global.tokenAuthResults) {
          global.tokenAuthResults = [];
        }

        global.tokenAuthResults.push({
          access_token,
          refresh_token,
          user: decodedUser,
          timestamp: Date.now()
        });

        // Clean up old results (older than 5 minutes)
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        global.tokenAuthResults = global.tokenAuthResults.filter(result => result.timestamp > fiveMinutesAgo);

        console.log('✅ [TOKEN_AUTH] Tokens stored for Electron app retrieval');

        // Redirect to success page instead of trying to redirect to Electron app
        const redirectUrl = `http://localhost:3000/auth/success?message=Authentication successful! You can now close this window and return to the app.`;
        console.log('🔍 [TOKEN_AUTH] Redirecting to success page');
        return res.redirect(redirectUrl);
      }

      // Handle cookie-based flow (original logic)
      console.log('🔍 [AUTH] Handling cookie-based OAuth callback');

      // Set HTTP-only cookies for desktop app
      // Access token cookie - short lived
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: false, // Set to false for localhost
        sameSite: 'lax',
        maxAge: 3600000, // 1 hour
        // Remove domain to allow sharing between ports
      });

      // Refresh token cookie - longer lived
      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: false, // Set to false for localhost
        sameSite: 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        path: '/auth', // Only available to /auth routes for security
        // Remove domain to allow sharing between ports
      });

      // Also set non-httpOnly cookies for Electron app to read
      res.cookie('electron_access_token', access_token, {
        httpOnly: false, // Allow JavaScript access for Electron
        secure: false,
        sameSite: 'lax',
        maxAge: 3600000, // 1 hour
        // Remove domain to allow sharing between ports
      });

      // Get session ID from state parameter
      let sessionId = null;
      if (req.query.state) {
        const stateParams = new URLSearchParams(req.query.state);
        sessionId = stateParams.get('session_id');
      }

      // Try to get session ID from global map as backup
      if (!sessionId && global.pendingSessions) {
        for (const [key, value] of global.pendingSessions.entries()) {
          sessionId = value;
          global.pendingSessions.delete(key);
          break;
        }
      }

      console.log('🔍 [AUTH] Session ID from state:', sessionId);

      if (sessionId) {
        console.log('🔍 [AUTH] Storing session data for desktop app:', sessionId);
        // Store session data in memory (you might want to use Redis for production)
        if (!global.desktopSessions) {
          global.desktopSessions = new Map();
        }
        global.desktopSessions.set(sessionId, {
          access_token,
          refresh_token,
          user: req.user,
          timestamp: Date.now(),
        });

        console.log('✅ [AUTH] Session data stored successfully');
        console.log('🔍 [AUTH] Current desktop sessions:', Array.from(global.desktopSessions.keys()));

        // Clean up old sessions (older than 10 minutes)
        const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
        for (const [key, value] of global.desktopSessions.entries()) {
          if (value.timestamp < tenMinutesAgo) {
            global.desktopSessions.delete(key);
          }
        }
      } else {
        console.log('❌ [AUTH] No session ID found, cannot store session data for desktop app');
      }

      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Successful</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                margin: 0;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
              }
              .container {
                background: white;
                color: #333;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                max-width: 400px;
              }
              .success {
                color: #52c41a;
                font-size: 48px;
                margin-bottom: 20px;
              }
              .title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 15px;
                color: #333;
              }
              .instructions {
                color: #666;
                font-size: 16px;
                line-height: 1.5;
              }
              .countdown {
                font-size: 14px;
                color: #999;
                margin-top: 20px;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="success">✅</div>
              <div class="title">Login Successful!</div>
              <div class="instructions">
                You have successfully logged in with Google.
                <br><br>
                Return to the Facebook Automation app to continue.
              </div>
              <div class="countdown">This window will close automatically in <span id="countdown">5</span> seconds...</div>
            </div>
            <script>
              // Store auth success flag for Electron app to detect
              localStorage.setItem('electron_auth_success', 'true');
              localStorage.setItem('electron_auth_timestamp', Date.now().toString());

              let countdown = 5;
              const countdownElement = document.getElementById('countdown');

              const timer = setInterval(() => {
                countdown--;
                countdownElement.textContent = countdown;

                if (countdown <= 0) {
                  clearInterval(timer);
                  window.close();
                }
              }, 1000);

              // Send success message to opener window (for popup mode)
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_SUCCESS' }, '*');
              }

              // Auto close after 5 seconds
              setTimeout(() => {
                try {
                  window.close();
                } catch (e) {
                  console.log('Could not auto-close window');
                }
              }, 5000);
            </script>
          </body>
        </html>
      `);
    } catch (error) {
      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Failed</title>
          </head>
          <body>
            <script>
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_FAILED' }, '*');
              }

                window.close();
            </script>
          </body>
        </html>
      `);
    }
  }

  @Get('auth/session')
  async getSessionData(@Query('sessionId') sessionId: string) {
    console.log('🔍 [AUTH] Checking session data for:', sessionId);

    // For testing, return a simple response first
    if (!sessionId) {
      throw new NotFoundException('Session ID is required');
    }

    if (!global.desktopSessions) {
      console.log('🔍 [AUTH] No desktop sessions found');
      throw new NotFoundException('Session not found');
    }

    const sessionData = global.desktopSessions.get(sessionId);
    if (!sessionData) {
      console.log('🔍 [AUTH] Session not found for ID:', sessionId);
      throw new NotFoundException('Session not found');
    }

    // Check if session is still valid (not older than 10 minutes)
    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
    if (sessionData.timestamp < tenMinutesAgo) {
      global.desktopSessions.delete(sessionId);
      console.log('🔍 [AUTH] Session expired for ID:', sessionId);
      throw new NotFoundException('Session expired');
    }

    console.log('✅ [AUTH] Session data found for:', sessionId);

    // Return session data and remove it (one-time use)
    global.desktopSessions.delete(sessionId);

    return {
      access_token: sessionData.access_token,
      refresh_token: sessionData.refresh_token,
      user: sessionData.user,
    };
  }

  @Get('auth/test')
  async testEndpoint() {
    return { message: 'Test endpoint working', timestamp: Date.now() };
  }

  @Get('auth/success')
  async authSuccess(@Res() res) {
    return res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Authentication Successful</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
            .container { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
            .success { color: #52c41a; font-size: 28px; margin-bottom: 20px; }
            .instructions { color: #666; font-size: 16px; line-height: 1.5; }
            .button { background: #1890ff; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-top: 20px; }
            .button:hover { background: #40a9ff; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="success">✅ Login Successful!</div>
            <div class="instructions">
              Your Google account has been successfully authenticated.
              <br><br>
              You can now return to the desktop application and use the regular login form with your email and password.
              <br><br>
              <strong>Note:</strong> You may need to refresh the desktop app or try logging in again.
            </div>
            <button class="button" onclick="window.close()">Close Window</button>
          </div>
        </body>
      </html>
    `);
  }

  @Get('auth/error')
  async authError(@Query('message') message: string, @Res() res) {
    return res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Authentication Failed</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
            .container { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
            .error { color: #ff4d4f; font-size: 28px; margin-bottom: 20px; }
            .instructions { color: #666; font-size: 16px; line-height: 1.5; }
            .button { background: #ff4d4f; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-top: 20px; }
            .button:hover { background: #ff7875; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="error">❌ Authentication Failed</div>
            <div class="instructions">
              There was an error during the authentication process.
              <br><br>
              Error: ${message || 'Unknown error'}
              <br><br>
              Please try again or contact support if the problem persists.
            </div>
            <button class="button" onclick="window.close()">Close Window</button>
          </div>
        </body>
      </html>
    `);
  }

  @Get('auth/discord/callback')
  @UseGuards(AuthGuard('discord'))
  async discordAuthCallback(@Req() req, @Res() res) {
    try {
      const { access_token, refresh_token } =
        await this.authService.validateDiscordLogin(req.user);

      // Set HTTP-only cookies
      // Access token cookie - short lived
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 3600000, // 1 hour
        // Remove domain to allow sharing between ports
      });

      // Refresh token cookie - longer lived
      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        // Remove path restriction to allow access from all endpoints
        // Remove domain to allow sharing between ports
      });

      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Successful</title>
          </head>
          <body>
            <script>
              // Send success message to opener window
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_SUCCESS' }, '*');
              }

              // Close this window
              window.close();
            </script>
          </body>
        </html>
      `);
    } catch (error) {
      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Failed</title>
          </head>
          <body>
            <script>
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_FAILED' }, '*');
              }

              window.close();
            </script>
          </body>
        </html>
      `);
    }
  }

  @Get('change-password')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('auth/change-password')
  showChangePassword() {
    return {};
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.authService.changePassword(userId, changePasswordDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to change password',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }



  // NEW BEARER TOKEN ENDPOINTS - DO NOT MODIFY EXISTING ENDPOINTS ABOVE

  @Get('auth/token/google')
  async googleTokenAuth(@Req() req, @Res() res) {
    // This endpoint initiates Google OAuth for token-based auth
    // We'll manually construct the OAuth URL with token=true state

    const googleClientId = process.env.GOOGLE_CLIENT_ID;
    const googleCallbackUrl = process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3000/auth/google/callback';
    const state = 'token=true'; // Mark this as token flow

    const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${googleClientId}&` +
      `redirect_uri=${encodeURIComponent(googleCallbackUrl)}&` +
      `response_type=code&` +
      `scope=email%20profile&` +
      `state=${encodeURIComponent(state)}`;

    console.log('🔍 [TOKEN_AUTH] Redirecting to Google OAuth URL:', googleAuthUrl);
    return res.redirect(googleAuthUrl);
  }



  @Post('auth/token/exchange')
  async exchangeTokens(@Body() body: { access_token: string; refresh_token: string }, @Res() res) {
    try {
      console.log('🔍 [TOKEN_AUTH] Token exchange request');

      // Verify the tokens
      const decoded = await this.authService.verifyToken(body.access_token);
      if (!decoded) {
        return res.status(401).json({
          success: false,
          message: 'Invalid access token',
        });
      }

      console.log('✅ [TOKEN_AUTH] Tokens verified successfully');

      return res.json({
        success: true,
        access_token: body.access_token,
        refresh_token: body.refresh_token,
        user: {
          id: decoded.id || (decoded as any).sub,
          email: decoded.email,
          role: decoded.role,
          status: decoded.status,
        }
      });
    } catch (error) {
      console.error('❌ [TOKEN_AUTH] Token exchange error:', error);
      return res.status(500).json({
        success: false,
        message: 'Token exchange failed',
      });
    }
  }

  @Post('auth/token/refresh')
  async refreshTokenOnly(@Body() body: { refresh_token: string }, @Res() res) {
    try {
      console.log('🔍 [TOKEN_AUTH] Token refresh request');

      const tokens = await this.authService.refreshToken(body.refresh_token);

      console.log('✅ [TOKEN_AUTH] Tokens refreshed successfully');

      return res.json({
        success: true,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
      });
    } catch (error) {
      console.error('❌ [TOKEN_AUTH] Token refresh error:', error);
      return res.status(401).json({
        success: false,
        message: 'Token refresh failed',
      });
    }
  }

  @Post('token/verify')
  async verifyTokenOnly(@Body() body: { access_token: string }, @Res() res) {
    try {
      console.log('🔍 [TOKEN_AUTH] Token verification request');

      const decoded = await this.authService.verifyToken(body.access_token);
      if (!decoded) {
        return res.status(401).json({
          success: false,
          message: 'Invalid access token',
        });
      }

      console.log('✅ [TOKEN_AUTH] Token verified successfully');

      return res.json({
        success: true,
        user: {
          id: decoded.id || (decoded as any).sub,
          email: decoded.email,
          role: decoded.role,
          status: decoded.status,
        }
      });
    } catch (error) {
      console.error('❌ [TOKEN_AUTH] Token verification error:', error);
      return res.status(401).json({
        success: false,
        message: 'Token verification failed',
      });
    }
  }

  // Endpoint for Electron app to retrieve OAuth tokens
  @Get('auth/token/retrieve')
  async retrieveTokens(@Res() res) {
    console.log('🔍 [TOKEN_AUTH] Token retrieval request from Electron app');

    try {
      if (!global.tokenAuthResults || global.tokenAuthResults.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No tokens available'
        });
      }

      // Get the most recent token result
      const latestResult = global.tokenAuthResults[global.tokenAuthResults.length - 1];

      // Mark as used and remove from global storage
      global.tokenAuthResults = [];

      console.log('✅ [TOKEN_AUTH] Tokens retrieved successfully for Electron app');

      return res.json({
        success: true,
        access_token: latestResult.access_token,
        refresh_token: latestResult.refresh_token,
        user: latestResult.user
      });
    } catch (error) {
      console.log('❌ [TOKEN_AUTH] Token retrieval error:', error);
      return res.status(500).json({
        success: false,
        message: 'Token retrieval failed',
        error: error.message
      });
    }
  }

  // Test endpoint to generate token manually for testing
  @Post('auth/token/test-generate')
  async generateTestToken(@Body() body: { email: string }, @Res() res) {
    console.log('🔍 [TOKEN_AUTH] Test token generation request for:', body.email);

    try {
      // Create a test user object
      const testUser = {
        id: body.email,
        email: body.email
      };

      const tokens = await this.authService.validateGoogleLogin(testUser);

      console.log('✅ [TOKEN_AUTH] Test tokens generated successfully');

      return res.json({
        success: true,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token
      });
    } catch (error) {
      console.log('❌ [TOKEN_AUTH] Test token generation error:', error);
      return res.status(500).json({
        success: false,
        message: 'Token generation failed',
        error: error.message
      });
    }
  }

  // EXISTING COOKIE-BASED ENDPOINTS BELOW - DO NOT MODIFY

  @Get('auth/get-token')
  async getToken(@Req() req: any, @Res() res: any) {
    try {
      console.log('🔍 [AUTH] Get token request received');
      console.log('🔍 [AUTH] Cookies:', req.cookies);

      const token = req.cookies['access_token'];

      if (!token) {
        console.log('❌ [AUTH] No access token found in cookies');
        return res.status(401).json({
          success: false,
          message: 'No access token found',
        });
      }

      // Verify token is valid
      const decoded = await this.authService.verifyToken(token);
      if (!decoded) {
        console.log('❌ [AUTH] Invalid access token');
        return res.status(401).json({
          success: false,
          message: 'Invalid access token',
        });
      }

      console.log('✅ [AUTH] Access token retrieved successfully');
      return res.json({
        success: true,
        access_token: token,
      });
    } catch (error) {
      console.error('❌ [AUTH] Error getting token:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  @Get('auth/verify')
  async verifyToken(@Req() req: any, @Res() res: any, @Query('sessionId') sessionId?: string) {
    try {
      console.log('🔍 [AUTH] Verify token request received');
      console.log('🔍 [AUTH] Session ID:', sessionId);
      console.log('🔍 [AUTH] Cookies:', req.cookies);

      // If sessionId is provided, check for session data
      if (sessionId) {
        console.log('🔍 [AUTH] Checking session data for:', sessionId);

        if (!global.desktopSessions) {
          console.log('🔍 [AUTH] No desktop sessions found');
          return res.status(404).json({
            valid: false,
            message: 'Session not found',
          });
        }

        const sessionData = global.desktopSessions.get(sessionId);
        if (!sessionData) {
          console.log('🔍 [AUTH] Session not found for ID:', sessionId);
          return res.status(404).json({
            valid: false,
            message: 'Session not found',
          });
        }

        // Check if session is still valid (not older than 10 minutes)
        const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
        if (sessionData.timestamp < tenMinutesAgo) {
          global.desktopSessions.delete(sessionId);
          console.log('🔍 [AUTH] Session expired for ID:', sessionId);
          return res.status(404).json({
            valid: false,
            message: 'Session expired',
          });
        }

        console.log('✅ [AUTH] Session data found for:', sessionId);

        // Return session data and remove it (one-time use)
        global.desktopSessions.delete(sessionId);

        return res.json({
          valid: true,
          access_token: sessionData.access_token,
          refresh_token: sessionData.refresh_token,
          user: sessionData.user,
          message: 'Session authenticated',
        });
      }

      // Original token verification logic
      const token = req.cookies['access_token'];

      if (!token) {
        console.log('❌ [AUTH] No access token found in cookies');
        return res.status(401).json({
          valid: false,
          message: 'No access token found',
        });
      }

      console.log('🔍 [AUTH] Token found, verifying...');
      const decoded = await this.authService.verifyToken(token);

      console.log('✅ [AUTH] Token verified successfully:', decoded);

      return res.json({
        valid: true,
        user: {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role,
          status: decoded.status,
        },
        message: 'Token is valid',
      });
    } catch (error) {
      console.error('❌ [AUTH] Token verification failed:', error);
      return res.status(401).json({
        valid: false,
        message: 'Invalid token',
        error: error.message,
      });
    }
  }

  @Get('auth/check-recent-login')
  async checkRecentLogin(@Res() res: any) {
    try {
      console.log('🔍 [AUTH] Checking for recent login...');

      // Check if there's a recent successful login in the last 2 minutes
      if (!global.recentLogins) {
        global.recentLogins = [];
      }

      const twoMinutesAgo = Date.now() - 2 * 60 * 1000;
      const recentLogin = global.recentLogins.find(login => login.timestamp > twoMinutesAgo);

      if (recentLogin) {
        console.log('✅ [AUTH] Found recent login:', recentLogin.email);

        // Mark as used but don't remove immediately (allow multiple checks within 30 seconds)
        if (!recentLogin.used) {
          recentLogin.used = true;
          recentLogin.usedAt = Date.now();
        }

        return res.json({
          success: true,
          user: recentLogin.user,
          access_token: recentLogin.access_token,
          refresh_token: recentLogin.refresh_token,
          message: 'Recent login found'
        });
      } else {
        console.log('❌ [AUTH] No recent login found');
        return res.status(404).json({
          success: false,
          message: 'No recent login found'
        });
      }
    } catch (error) {
      console.error('❌ [AUTH] Check recent login error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message,
      });
    }
  }

  @Get('users')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async getAllUsers(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('search') search?: string,
    @Query('status') status?: string,
  ) {
    try {
      const pageNum = page ? parseInt(page, 10) : 1;
      const limitNum = limit ? parseInt(limit, 10) : 50;

      return await this.usersService.findAllUsers(pageNum, limitNum, search, status);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve users',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
