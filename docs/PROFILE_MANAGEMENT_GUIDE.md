# Profile Management System - User Guide

## 📋 Overview

The Profile Management System allows administrators to organize and manage antidetect browser profiles through a hierarchical structure of Profile Groups, Profiles, and User Access permissions.

## 🏗️ System Architecture

```
Profile Groups (Admin creates)
    ├── Profiles (Linked to Accounts)
    │   ├── Account 1 → Browser Profile
    │   ├── Account 2 → Browser Profile
    │   └── Account N → Browser Profile
    └── User Access (Permissions)
        ├── User A (read access)
        ├── User B (full access)
        └── User C (write access)
```

## 🚀 Getting Started

### Prerequisites
- Admin role access
- Backend server running on `http://localhost:3000`
- Frontend application running
- Existing accounts in the system

### Accessing Profile Management
1. <PERSON>gin as an admin user
2. Navigate to the sidebar menu
3. Click on "Manage Profile" (visible only to admins)

## 📊 Main Features

### 1. Profile Groups Management

**Purpose**: Organize profiles into logical groups (e.g., "Marketing Team", "Sales Team", "Development")

**Features**:
- ✅ Create new profile groups
- ✅ Edit existing groups
- ✅ Delete groups (only if no profiles assigned)
- ✅ View profile and user counts

**How to use**:
1. Go to "Profile Groups" tab
2. Click "Create Profile Group"
3. Fill in name and description
4. Save to create the group

### 2. Profiles Management

**Purpose**: Link individual accounts to profile groups for browser launching

**Features**:
- ✅ Create profiles linked to accounts
- ✅ Assign profiles to profile groups
- ✅ Launch Camoufox browser directly
- ✅ Edit and delete profiles
- ✅ View profile status and details

**How to use**:
1. Go to "Profiles" tab
2. Click "Create Profile"
3. Select an account and profile group
4. Add name and description
5. Save to create the profile
6. Use "Launch Browser" button to start Camoufox

### 3. User Assignments

**Purpose**: Grant users access to specific profile groups with configurable permissions

**Features**:
- ✅ Assign users to profile groups
- ✅ Configure granular permissions
- ✅ Set access expiration dates
- ✅ Manage access levels (read/write/full)

**Permission Types**:
- `can_launch_browser`: Allow launching browsers
- `can_view_profiles`: Allow viewing profile details
- `can_export_data`: Allow exporting profile data
- `access_level`: Overall access level (read/write/full)

**How to use**:
1. Go to "User Assignments" tab
2. Click "Assign User to Group"
3. Select user and profile group
4. Configure permissions
5. Set expiration date (optional)
6. Save assignment

## 🔧 Technical Details

### API Endpoints

#### Profile Groups
- `GET /profiles/groups` - List all profile groups
- `POST /profiles/groups` - Create new profile group
- `PUT /profiles/groups/:id` - Update profile group
- `DELETE /profiles/groups/:id` - Delete profile group

#### Profiles
- `GET /profiles/items` - List all profiles
- `POST /profiles/items` - Create new profile
- `PUT /profiles/items/:id` - Update profile
- `DELETE /profiles/items/:id` - Delete profile

#### User Access
- `GET /profiles/group-access` - List user assignments
- `POST /profiles/group-access` - Create user assignment
- `PUT /profiles/group-access/:id` - Update assignment
- `DELETE /profiles/group-access/:id` - Remove assignment

#### Browser Integration
- `POST /api/profiles/:accountId/launch-browser` - Launch Camoufox browser

### Database Schema

```sql
-- Profile Groups
CREATE TABLE profile_groups (
    id SERIAL PRIMARY KEY,
    name VARCHAR UNIQUE NOT NULL,
    description TEXT,
    created_by_admin_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Profiles
CREATE TABLE profiles (
    id SERIAL PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    account_id INTEGER NOT NULL,
    profile_group_id INTEGER NOT NULL,
    status VARCHAR DEFAULT 'active',
    created_by_admin_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User Profile Group Access
CREATE TABLE user_profile_group_access (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    profile_group_id INTEGER NOT NULL,
    permissions JSONB NOT NULL,
    granted_by_admin_id INTEGER NOT NULL,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
./scripts/run-tests.sh

# Backend tests only
cd auto-login && npm run test

# Frontend tests only
cd frontend && npm test

# Integration tests
cd frontend && npm test -- --testPathPattern=integration
```

### Test Coverage
- ✅ Unit tests for ProfilesService
- ✅ Component tests for ProfileList, ProfileGroupList
- ✅ Integration tests for ManageProfiles page
- ✅ API endpoint tests
- ✅ Browser launch functionality tests

## 🔒 Security Features

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (admin only)
- Protected API endpoints
- Session management

### Data Validation
- Input sanitization
- DTO validation with class-validator
- SQL injection prevention
- XSS protection

## 🚨 Troubleshooting

### Common Issues

**1. "Access Denied" message**
- Solution: Ensure you're logged in as an admin user

**2. "No account associated with this profile"**
- Solution: Edit the profile and select a valid account

**3. Browser launch fails**
- Solution: Check if the account exists and Camoufox service is running

**4. API errors**
- Solution: Verify backend server is running on port 3000

### Error Messages
- `Failed to load profile groups` - Backend connection issue
- `Profile group name already exists` - Duplicate name validation
- `Cannot delete group with profiles` - Business logic constraint
- `User is not active` - User status validation

## 📈 Best Practices

### Organization
1. Create logical profile groups based on teams or purposes
2. Use descriptive names for profiles and groups
3. Regularly review and clean up unused profiles
4. Set appropriate expiration dates for user access

### Security
1. Regularly audit user permissions
2. Remove access for inactive users
3. Use least privilege principle
4. Monitor browser launch activities

### Performance
1. Limit the number of profiles per group
2. Regular cleanup of expired access records
3. Monitor system resources during browser launches

## 🔄 Workflow Example

### Setting up a Marketing Team
1. **Create Profile Group**: "Marketing Team"
2. **Add Profiles**: Link Facebook accounts to the group
3. **Assign Users**: Grant marketing team members access
4. **Configure Permissions**: Allow browser launch and view access
5. **Launch Browsers**: Team members can now launch browsers for their assigned profiles

## 📞 Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.

---

*Last updated: July 26, 2025*
