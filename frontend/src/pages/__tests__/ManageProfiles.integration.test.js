/**
 * ManageProfiles Integration Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { message } from 'antd';
import ManageProfiles from '../ManageProfiles';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

// Mock AuthContext
const mockAuthContext = {
  user: { id: 1, email: '<EMAIL>', role: 'admin' },
  login: jest.fn(),
  logout: jest.fn(),
  loading: false,
};

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext,
  AuthProvider: ({ children }) => children,
}));

describe('ManageProfiles Integration Tests', () => {
  const mockApiResponses = {
    profileGroups: {
      data: [
        {
          id: 1,
          name: 'Test Group 1',
          description: 'Test Description',
          created_by_admin_id: 1,
          created_at: '2024-01-01T00:00:00Z',
          profiles: [],
          userAccess: [],
        },
      ],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
    },
    profiles: {
      data: [
        {
          id: 1,
          name: 'Test Profile 1',
          account_id: 1,
          profile_group_id: 1,
          profile_group: { name: 'Test Group 1' },
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
        },
      ],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
    },
    users: {
      data: [
        {
          id: 2,
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
        },
      ],
      pagination: { page: 1, limit: 50, total: 1, totalPages: 1 },
    },
    accounts: {
      data: [
        {
          id: 1,
          name: 'Test Account',
          website_url: 'https://facebook.com',
          username: 'testuser',
        },
      ],
      meta: { page: 1, limit: 50, total: 1, totalPages: 1 },
    },
    assignments: {
      data: [],
      pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    fetch.mockClear();
    
    // Setup default fetch responses
    fetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.profileGroups,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.profiles,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.assignments,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.users,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.accounts,
      });
  });

  const renderWithRouter = (component) => {
    return render(
      <BrowserRouter>
        <AuthProvider>
          {component}
        </AuthProvider>
      </BrowserRouter>
    );
  };

  test('renders ManageProfiles page correctly', async () => {
    renderWithRouter(<ManageProfiles />);

    // Check if page title is rendered
    expect(screen.getByText('Profile Management')).toBeInTheDocument();

    // Check if tabs are rendered
    await waitFor(() => {
      expect(screen.getByText('Profile Groups')).toBeInTheDocument();
      expect(screen.getByText('Profiles')).toBeInTheDocument();
      expect(screen.getByText('User Assignments')).toBeInTheDocument();
    });
  });

  test('loads and displays data correctly', async () => {
    renderWithRouter(<ManageProfiles />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Test Group 1')).toBeInTheDocument();
      expect(screen.getByText('Test Profile 1')).toBeInTheDocument();
    });

    // Verify API calls were made
    expect(fetch).toHaveBeenCalledWith('http://localhost:3000/profiles/groups', {
      credentials: 'include',
    });
    expect(fetch).toHaveBeenCalledWith('http://localhost:3000/profiles/items', {
      credentials: 'include',
    });
    expect(fetch).toHaveBeenCalledWith('http://localhost:3000/auth/users', {
      credentials: 'include',
    });
    expect(fetch).toHaveBeenCalledWith('http://localhost:3000/accounts', {
      credentials: 'include',
    });
  });

  test('handles API errors gracefully', async () => {
    // Mock API error
    fetch.mockRejectedValueOnce(new Error('Network error'));

    renderWithRouter(<ManageProfiles />);

    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('Failed to load profile groups');
    });
  });

  test('switches between tabs correctly', async () => {
    renderWithRouter(<ManageProfiles />);

    await waitFor(() => {
      expect(screen.getByText('Test Group 1')).toBeInTheDocument();
    });

    // Click on Profiles tab
    const profilesTab = screen.getByText('Profiles');
    fireEvent.click(profilesTab);

    await waitFor(() => {
      expect(screen.getByText('Test Profile 1')).toBeInTheDocument();
    });

    // Click on User Assignments tab
    const assignmentsTab = screen.getByText('User Assignments');
    fireEvent.click(assignmentsTab);

    // Should show empty state for assignments
    await waitFor(() => {
      expect(screen.getByText('No user assignments found')).toBeInTheDocument();
    });
  });

  test('creates new profile group successfully', async () => {
    // Mock successful creation
    fetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.profileGroups,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.profiles,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.assignments,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.users,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.accounts,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ id: 2, name: 'New Group', description: 'New Description' }),
      });

    renderWithRouter(<ManageProfiles />);

    await waitFor(() => {
      expect(screen.getByText('Create Profile Group')).toBeInTheDocument();
    });

    // Click create button
    const createButton = screen.getByText('Create Profile Group');
    fireEvent.click(createButton);

    // Modal should open
    await waitFor(() => {
      expect(screen.getByText('Create Profile Group')).toBeInTheDocument();
    });
  });

  test('handles browser launch from profile list', async () => {
    // Mock successful browser launch
    fetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.profileGroups,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.profiles,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.assignments,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.users,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponses.accounts,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

    renderWithRouter(<ManageProfiles />);

    // Switch to Profiles tab
    await waitFor(() => {
      const profilesTab = screen.getByText('Profiles');
      fireEvent.click(profilesTab);
    });

    await waitFor(() => {
      expect(screen.getByText('Test Profile 1')).toBeInTheDocument();
    });

    // Find and click launch browser button
    const launchButtons = screen.getAllByRole('button');
    const launchButton = launchButtons.find(btn => 
      btn.querySelector('.anticon-chrome')
    );
    
    if (launchButton) {
      fireEvent.click(launchButton);

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:3000/api/profiles/1/launch-browser',
          expect.objectContaining({
            method: 'POST',
            credentials: 'include',
          })
        );
      });
    }
  });

  test('redirects non-admin users', async () => {
    // Mock non-admin user
    const nonAdminContext = {
      ...mockAuthContext,
      user: { id: 2, email: '<EMAIL>', role: 'user' },
    };

    jest.mocked(require('../../contexts/AuthContext').useAuth).mockReturnValue(nonAdminContext);

    renderWithRouter(<ManageProfiles />);

    await waitFor(() => {
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.getByText('You do not have permission to access this page.')).toBeInTheDocument();
    });
  });
});
