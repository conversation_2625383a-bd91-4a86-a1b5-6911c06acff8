/**
 * ProfileForm Component - Form for creating/editing profiles
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, message, Modal, Space } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { apiService } from '../../services/api';

const { TextArea } = Input;
const { Option } = Select;

const ProfileForm = ({ 
  visible, 
  onCancel, 
  onSuccess, 
  editingProfile = null,
  profileGroups = [],
  accounts = [],
  loading = false 
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (visible) {
      if (editingProfile) {
        form.setFieldsValue({
          name: editingProfile.name,
          description: editingProfile.description || '',
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, editingProfile, form]);

  const handleSubmit = async (values) => {
    setSubmitting(true);
    try {
      console.log('Submitting profile form with values:', values);

      // Convert form values to FastAPI format
      const profileData = {
        name: values.name,
        proxy_config: {
          type: 'no_proxy', // Default proxy type
          host: '',
          port: null,
          username: '',
          password: ''
        }
      };

      console.log('Converted profile data:', profileData);

      let response;
      if (editingProfile) {
        // Use FastAPI update endpoint
        response = await apiService.updateProfile(editingProfile.id, profileData);
      } else {
        // Use FastAPI create endpoint
        response = await apiService.createProfile(profileData);
      }

      console.log('Profile operation response:', response);

      message.success(
        editingProfile
          ? 'Profile updated successfully!'
          : 'Profile created successfully!'
      );
      form.resetFields();
      onSuccess(response);
    } catch (error) {
      console.error('Profile operation error:', error);
      console.error('Error response:', error.response?.data);

      // Check if it's actually a success but wrapped in error
      if (error.response?.status === 200 || error.response?.data?.success) {
        console.log('Success response wrapped in error, processing...');
        message.success(
          editingProfile
            ? 'Profile updated successfully!'
            : 'Profile created successfully!'
        );
        form.resetFields();
        onSuccess(error.response.data);
      } else {
        const errorMessage = error.response?.data?.detail || error.response?.data?.message || error.message || 'Operation failed';
        message.error(`Operation failed: ${errorMessage}`);
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Modal
      title={
        <Space>
          {editingProfile ? <EditOutlined /> : <PlusOutlined />}
          {editingProfile ? 'Edit Profile' : 'Create New Profile'}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="large"
      >
        <Form.Item
          name="name"
          label="Profile Name"
          rules={[
            { required: true, message: 'Please enter profile name!' },
            { max: 255, message: 'Profile name must be less than 255 characters!' },
          ]}
        >
          <Input 
            placeholder="Enter profile name (e.g., profile1, profile2)"
            showCount
            maxLength={255}
          />
        </Form.Item>

        {/* Note: FastAPI profiles don't require profile_group_id or account_id */}
        {/* These fields are for NestJS profiles only */}

        <Form.Item
          name="description"
          label="Description"
          rules={[
            { max: 1000, message: 'Description must be less than 1000 characters!' },
          ]}
        >
          <TextArea
            placeholder="Enter profile description (optional)"
            rows={3}
            showCount
            maxLength={1000}
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={submitting}
              icon={editingProfile ? <EditOutlined /> : <PlusOutlined />}
            >
              {editingProfile ? 'Update Profile' : 'Create Profile'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ProfileForm;
