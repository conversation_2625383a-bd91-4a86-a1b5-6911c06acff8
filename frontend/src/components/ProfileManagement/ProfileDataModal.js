/**
 * ProfileDataModal Component - Display and manage profile data
 */

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Tabs, 
  Card, 
  Typography, 
  Space, 
  Button, 
  message, 
  Spin,
  Empty,
  Tag,
  Descriptions
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  HistoryOutlined,
  SettingOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { apiService } from '../../services/api';
import dayjs from 'dayjs';

const { Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const ProfileDataModal = ({ 
  visible, 
  onClose, 
  profile 
}) => {
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (visible && profile) {
      loadProfileData();
    }
  }, [visible, profile]);

  const loadProfileData = async () => {
    if (!profile?.account_id) return;

    try {
      setLoading(true);
      const response = await apiService.getProfileData(profile.account_id);
      
      if (response.data.success) {
        setProfileData(response.data.data);
      } else {
        message.error('Failed to load profile data');
      }
    } catch (error) {
      console.error('Load profile data error:', error);
      message.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!profile?.account_id) return;

    try {
      setSaving(true);

      // First, capture real browser data from the active browser session
      message.loading('Đang capture dữ liệu từ browser...', 0);

      try {
        // Use the profile ID to capture browser data
        const captureResponse = await apiService.post(`/api/profiles/${profile.id}/capture-browser-data`);

        message.destroy(); // Clear loading message

        if (!captureResponse.success) {
          message.error(`Lỗi capture data: ${captureResponse.message}`);
          return;
        }

        const realBrowserData = captureResponse.data;
        message.loading('Đã capture dữ liệu thành công, đang lưu...', 0);

        // Convert the captured data to the format expected by saveProfileData
        const newProfileData = {
          localStorage: {},
          indexedDB: {
            databases: [],
            data: {}
          },
          history: [],
          cookies: realBrowserData.cookies || []
        };

        // Convert localStorage array to object format
        if (realBrowserData.localStorage && Array.isArray(realBrowserData.localStorage)) {
          realBrowserData.localStorage.forEach(item => {
            if (item.key && item.value) {
              newProfileData.localStorage[item.key] = item.value;
            }
          });
        }

        // Add current page to history if available
        if (realBrowserData.current_page && realBrowserData.current_page.url) {
          newProfileData.history.push({
            url: realBrowserData.current_page.url,
            title: realBrowserData.current_page.title || 'Untitled',
            visitTime: realBrowserData.captured_at || new Date().toISOString()
          });
        }

        message.destroy(); // Clear loading message

        const response = await apiService.saveProfileData(profile.account_id, newProfileData);

      } catch (captureError) {
        message.destroy(); // Clear loading message
        console.error('Capture error:', captureError);
        message.error('Không thể capture dữ liệu từ browser. Có thể browser chưa được launch hoặc đã đóng.');
        return;
      }

      if (response.data.success) {
        message.success('Profile data saved successfully');
        // Reload the data to show updated information
        await loadProfileData();
      } else {
        message.error(response.data.message || 'Failed to save profile data');
      }
    } catch (error) {
      console.error('Save profile error:', error);
      message.error('Failed to save profile data');
    } finally {
      setSaving(false);
    }
  };

  const renderLocalStorageTab = () => {
    const localStorage = profileData?.profileData?.localStorage || {};
    
    if (Object.keys(localStorage).length === 0) {
      return <Empty description="No localStorage data" />;
    }

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {Object.entries(localStorage).map(([key, value]) => (
          <Card key={key} size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Key">
                <Tag color="blue">{key}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Value">
                <Paragraph 
                  copyable 
                  style={{ margin: 0, maxWidth: '400px' }}
                  ellipsis={{ rows: 2, expandable: true }}
                >
                  {typeof value === 'string' ? value : JSON.stringify(value)}
                </Paragraph>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        ))}
      </Space>
    );
  };

  const renderIndexedDBTab = () => {
    const indexedDB = profileData?.profileData?.indexedDB || {};
    
    if (Object.keys(indexedDB).length === 0) {
      return <Empty description="No IndexedDB data" />;
    }

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Card size="small">
          <Descriptions column={1} size="small">
            <Descriptions.Item label="Databases">
              <Space wrap>
                {(indexedDB.databases || []).map(db => (
                  <Tag key={db} color="green">{db}</Tag>
                ))}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="Data">
              <Paragraph 
                copyable 
                style={{ margin: 0 }}
                ellipsis={{ rows: 4, expandable: true }}
              >
                {JSON.stringify(indexedDB.data || {}, null, 2)}
              </Paragraph>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </Space>
    );
  };

  const renderHistoryTab = () => {
    const history = profileData?.profileData?.history || [];
    
    if (history.length === 0) {
      return <Empty description="No browsing history" />;
    }

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {history.map((item, index) => (
          <Card key={index} size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="URL">
                <a href={item.url} target="_blank" rel="noopener noreferrer">
                  {item.url}
                </a>
              </Descriptions.Item>
              <Descriptions.Item label="Title">
                <Text>{item.title}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Visit Time">
                <Text type="secondary">
                  {dayjs(item.visitTime).format('MMM DD, YYYY HH:mm:ss')}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        ))}
      </Space>
    );
  };

  const renderCookiesTab = () => {
    const cookies = profileData?.profileData?.cookies || [];
    
    if (cookies.length === 0) {
      return <Empty description="No cookies data" />;
    }

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {cookies.map((cookie, index) => (
          <Card key={index} size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Name">
                <Tag color="orange">{cookie.name}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Value">
                <Paragraph 
                  copyable 
                  style={{ margin: 0 }}
                  ellipsis={{ rows: 1, expandable: true }}
                >
                  {cookie.value}
                </Paragraph>
              </Descriptions.Item>
              <Descriptions.Item label="Domain">
                <Text type="secondary">{cookie.domain}</Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        ))}
      </Space>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <DatabaseOutlined />
          Profile Data - {profile?.name}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="refresh" icon={<ReloadOutlined />} onClick={loadProfileData}>
          Refresh
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          icon={<SaveOutlined />} 
          loading={saving}
          onClick={handleSaveProfile}
        >
          Save Current Profile Data
        </Button>,
        <Button key="close" onClick={onClose}>
          Close
        </Button>,
      ]}
    >
      <Spin spinning={loading}>
        {profileData ? (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card size="small">
              <Descriptions column={2} size="small">
                <Descriptions.Item label="Account ID">
                  {profileData.accountId}
                </Descriptions.Item>
                <Descriptions.Item label="Sync Status">
                  <Tag color={profileData.syncStatus === 'synced' ? 'green' : 'orange'}>
                    {profileData.syncStatus?.toUpperCase()}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Last Sync">
                  {profileData.lastSync ? 
                    dayjs(profileData.lastSync).format('MMM DD, YYYY HH:mm:ss') : 
                    'Never'
                  }
                </Descriptions.Item>
                <Descriptions.Item label="Saved By">
                  User ID: {profileData.profileData?.savedBy || 'N/A'}
                </Descriptions.Item>
              </Descriptions>
            </Card>

            <Tabs defaultActiveKey="localStorage">
              <TabPane 
                tab={
                  <Space>
                    <SettingOutlined />
                    localStorage
                  </Space>
                } 
                key="localStorage"
              >
                {renderLocalStorageTab()}
              </TabPane>
              
              <TabPane 
                tab={
                  <Space>
                    <DatabaseOutlined />
                    IndexedDB
                  </Space>
                } 
                key="indexedDB"
              >
                {renderIndexedDBTab()}
              </TabPane>
              
              <TabPane 
                tab={
                  <Space>
                    <HistoryOutlined />
                    History
                  </Space>
                } 
                key="history"
              >
                {renderHistoryTab()}
              </TabPane>
              
              <TabPane
                tab={
                  <Space>
                    <GlobalOutlined />
                    Cookies
                  </Space>
                }
                key="cookies"
              >
                {renderCookiesTab()}
              </TabPane>
            </Tabs>
          </Space>
        ) : (
          <Empty description="No profile data available" />
        )}
      </Spin>
    </Modal>
  );
};

export default ProfileDataModal;
