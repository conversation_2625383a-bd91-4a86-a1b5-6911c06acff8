/**
 * ProfileGroupList Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { message } from 'antd';
import ProfileGroupList from '../ProfileGroupList';

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

describe('ProfileGroupList Component', () => {
  const mockProfileGroups = [
    {
      id: 1,
      name: 'Test Group 1',
      description: 'Test description 1',
      created_by_admin_id: 1,
      created_at: '2024-01-01T00:00:00Z',
      profiles: [{ id: 1 }, { id: 2 }],
      userAccess: [{ id: 1 }],
    },
    {
      id: 2,
      name: 'Test Group 2',
      description: 'Test description 2',
      created_by_admin_id: 1,
      created_at: '2024-01-02T00:00:00Z',
      profiles: [{ id: 3 }],
      userAccess: [],
    },
  ];

  const mockProps = {
    profileGroups: mockProfileGroups,
    loading: false,
    onEdit: jest.fn(),
    onCreate: jest.fn(),
    onRefresh: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    fetch.mockClear();
  });

  test('renders profile group list correctly', () => {
    render(<ProfileGroupList {...mockProps} />);
    
    expect(screen.getByText('Test Group 1')).toBeInTheDocument();
    expect(screen.getByText('Test Group 2')).toBeInTheDocument();
    expect(screen.getByText('Test description 1')).toBeInTheDocument();
    expect(screen.getByText('Test description 2')).toBeInTheDocument();
  });

  test('shows loading state', () => {
    render(<ProfileGroupList {...mockProps} loading={true} />);
    
    expect(document.querySelector('.ant-spin')).toBeInTheDocument();
  });

  test('displays correct profile and user counts', () => {
    render(<ProfileGroupList {...mockProps} />);
    
    // Check profile counts
    expect(screen.getByText('2')).toBeInTheDocument(); // 2 profiles in first group
    expect(screen.getByText('1')).toBeInTheDocument(); // 1 profile in second group
    
    // Check user counts
    expect(screen.getByText('1')).toBeInTheDocument(); // 1 user in first group
    expect(screen.getByText('0')).toBeInTheDocument(); // 0 users in second group
  });

  test('handles delete profile group successfully', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true }),
    });

    render(<ProfileGroupList {...mockProps} />);
    
    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(btn => 
      btn.querySelector('.anticon-delete')
    );
    
    fireEvent.click(deleteButton);

    // Confirm deletion in popconfirm
    const confirmButton = screen.getByText('Yes');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/profiles/groups/1',
        expect.objectContaining({
          method: 'DELETE',
          credentials: 'include',
        })
      );
    });

    expect(message.success).toHaveBeenCalledWith('Profile group deleted successfully!');
    expect(mockProps.onRefresh).toHaveBeenCalled();
  });

  test('handles delete profile group error', async () => {
    fetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({ message: 'Cannot delete group with profiles' }),
    });

    render(<ProfileGroupList {...mockProps} />);
    
    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(btn => 
      btn.querySelector('.anticon-delete')
    );
    
    fireEvent.click(deleteButton);

    // Confirm deletion in popconfirm
    const confirmButton = screen.getByText('Yes');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('Cannot delete group with profiles');
    });
  });

  test('calls onCreate when create button is clicked', () => {
    render(<ProfileGroupList {...mockProps} />);
    
    const createButton = screen.getByText('Create Profile Group');
    fireEvent.click(createButton);

    expect(mockProps.onCreate).toHaveBeenCalled();
  });

  test('calls onEdit when edit button is clicked', () => {
    render(<ProfileGroupList {...mockProps} />);
    
    const editButtons = screen.getAllByRole('button');
    const editButton = editButtons.find(btn => 
      btn.querySelector('.anticon-edit')
    );
    
    fireEvent.click(editButton);

    expect(mockProps.onEdit).toHaveBeenCalledWith(mockProfileGroups[0]);
  });

  test('shows empty state when no profile groups', () => {
    render(<ProfileGroupList {...mockProps} profileGroups={[]} />);
    
    expect(screen.getByText('No profile groups found')).toBeInTheDocument();
  });

  test('formats dates correctly', () => {
    render(<ProfileGroupList {...mockProps} />);
    
    // Check if dates are displayed (dayjs formatting)
    expect(screen.getByText(/2024/)).toBeInTheDocument();
  });

  test('handles network error during delete', async () => {
    fetch.mockRejectedValueOnce(new Error('Network error'));

    render(<ProfileGroupList {...mockProps} />);
    
    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(btn => 
      btn.querySelector('.anticon-delete')
    );
    
    fireEvent.click(deleteButton);

    // Confirm deletion in popconfirm
    const confirmButton = screen.getByText('Yes');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('Network error occurred');
    });
  });
});
