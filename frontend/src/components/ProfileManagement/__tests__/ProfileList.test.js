/**
 * ProfileList Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { message } from 'antd';
import ProfileList from '../ProfileList';

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

describe('ProfileList Component', () => {
  const mockProfiles = [
    {
      id: 1,
      name: 'Test Profile 1',
      description: 'Test description',
      account_id: 1,
      profile_group: { name: 'Test Group' },
      status: 'active',
      created_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 2,
      name: 'Test Profile 2',
      account_id: 2,
      profile_group: { name: 'Test Group 2' },
      status: 'inactive',
      created_at: '2024-01-02T00:00:00Z',
    },
  ];

  const mockProps = {
    profiles: mockProfiles,
    loading: false,
    accounts: [
      { id: 1, name: 'Account 1' },
      { id: 2, name: 'Account 2' },
    ],
    profileGroups: [
      { id: 1, name: 'Test Group' },
      { id: 2, name: 'Test Group 2' },
    ],
    onEdit: jest.fn(),
    onCreate: jest.fn(),
    onRefresh: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    fetch.mockClear();
  });

  test('renders profile list correctly', () => {
    render(<ProfileList {...mockProps} />);
    
    expect(screen.getByText('Test Profile 1')).toBeInTheDocument();
    expect(screen.getByText('Test Profile 2')).toBeInTheDocument();
    expect(screen.getByText('Test description')).toBeInTheDocument();
  });

  test('shows loading state', () => {
    render(<ProfileList {...mockProps} loading={true} />);
    
    // Antd Table shows loading spinner when loading prop is true
    expect(document.querySelector('.ant-spin')).toBeInTheDocument();
  });

  test('handles browser launch successfully', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true }),
    });

    render(<ProfileList {...mockProps} />);
    
    const launchButtons = screen.getAllByRole('button');
    const launchButton = launchButtons.find(btn => 
      btn.querySelector('.anticon-chrome')
    );
    
    fireEvent.click(launchButton);

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/profiles/1/launch-browser',
        expect.objectContaining({
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
        })
      );
    });

    expect(message.success).toHaveBeenCalledWith('Browser launched successfully');
  });

  test('handles browser launch error - no account', async () => {
    const profilesWithoutAccount = [
      {
        ...mockProfiles[0],
        account_id: null,
      },
    ];

    render(<ProfileList {...mockProps} profiles={profilesWithoutAccount} />);
    
    const launchButtons = screen.getAllByRole('button');
    const launchButton = launchButtons.find(btn => 
      btn.querySelector('.anticon-chrome')
    );
    
    fireEvent.click(launchButton);

    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('No account associated with this profile');
    });

    expect(fetch).not.toHaveBeenCalled();
  });

  test('handles browser launch API error', async () => {
    fetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({ message: 'API Error' }),
    });

    render(<ProfileList {...mockProps} />);
    
    const launchButtons = screen.getAllByRole('button');
    const launchButton = launchButtons.find(btn => 
      btn.querySelector('.anticon-chrome')
    );
    
    fireEvent.click(launchButton);

    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('API Error');
    });
  });

  test('handles delete profile', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true }),
    });

    render(<ProfileList {...mockProps} />);
    
    // Find delete button and click
    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(btn => 
      btn.querySelector('.anticon-delete')
    );
    
    fireEvent.click(deleteButton);

    // Confirm deletion in popconfirm
    const confirmButton = screen.getByText('Yes');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/profiles/items/1',
        expect.objectContaining({
          method: 'DELETE',
          credentials: 'include',
        })
      );
    });

    expect(message.success).toHaveBeenCalledWith('Profile deleted successfully!');
    expect(mockProps.onRefresh).toHaveBeenCalled();
  });

  test('calls onCreate when create button is clicked', () => {
    render(<ProfileList {...mockProps} />);
    
    const createButton = screen.getByText('Create Profile');
    fireEvent.click(createButton);

    expect(mockProps.onCreate).toHaveBeenCalled();
  });

  test('calls onEdit when edit button is clicked', () => {
    render(<ProfileList {...mockProps} />);
    
    const editButtons = screen.getAllByRole('button');
    const editButton = editButtons.find(btn => 
      btn.querySelector('.anticon-edit')
    );
    
    fireEvent.click(editButton);

    expect(mockProps.onEdit).toHaveBeenCalledWith(mockProfiles[0]);
  });

  test('displays correct status tags', () => {
    render(<ProfileList {...mockProps} />);
    
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Inactive')).toBeInTheDocument();
  });

  test('displays profile group names', () => {
    render(<ProfileList {...mockProps} />);
    
    expect(screen.getByText('Test Group')).toBeInTheDocument();
    expect(screen.getByText('Test Group 2')).toBeInTheDocument();
  });
});
