#!/usr/bin/env python3
"""
Test script to debug browser data capture with detailed logging
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import AsyncSessionLocal
from app.models.profile import Profile
from app.services.profile_manager import AntidetectProfileManager
from sqlalchemy import select

async def test_capture_debug():
    """Test browser data capture with debug logging"""
    
    print("🧪 Testing Browser Data Capture with Debug Logging...")
    
    # Test 1: Check if profile 5 exists
    print("\n1️⃣ Checking if profile 5 exists...")
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Profile).where(Profile.id == 5))
        profile = result.scalar_one_or_none()
        
        if profile:
            print(f"✅ Profile 5 exists: {profile.name}")
            print(f"   📁 Profile path: {profile.profile_path}")
        else:
            print("❌ Profile 5 not found, trying profile 1...")
            result = await db.execute(select(Profile).where(Profile.id == 1))
            profile = result.scalar_one_or_none()
            if not profile:
                print("❌ No profiles found")
                return
            print(f"✅ Using profile 1: {profile.name}")
    
    # Test 2: Initialize ProfileManager
    print("\n2️⃣ Initializing ProfileManager...")
    try:
        profile_manager = AntidetectProfileManager()
        print("✅ ProfileManager initialized")
    except Exception as e:
        print(f"❌ ProfileManager initialization failed: {e}")
        return
    
    # Test 3: Launch browser
    print("\n3️⃣ Launching browser...")
    try:
        launch_result = await profile_manager.launch_browser(
            profile_path=profile.profile_path,
            proxy_config=None,
            headless=False,
            browser_data=None
        )
        
        if not launch_result.get('success'):
            print(f"❌ Browser launch failed: {launch_result.get('message')}")
            return
            
        print(f"✅ Browser launched successfully")
        
        # Get browser profile ID for capture operations
        import os
        browser_profile_id = os.path.basename(profile.profile_path)
        print(f"   🔍 Browser profile ID: {browser_profile_id}")
        
        # Wait a moment for browser to be ready
        await asyncio.sleep(3)
        
    except Exception as e:
        print(f"❌ Browser launch failed: {e}")
        return
    
    # Test 4: Check active browsers before capture
    print("\n4️⃣ Checking active browsers...")
    try:
        # Check CamoufoxBrowserManager active browsers
        camoufox_browsers = list(profile_manager.camoufox_manager.active_browsers.keys())
        print(f"   🔍 CamoufoxBrowserManager active browsers: {camoufox_browsers}")
        
        # Check ProfileManager active browsers
        legacy_browsers = list(profile_manager.active_browsers.keys())
        print(f"   🔍 ProfileManager active browsers: {legacy_browsers}")
        
        # Check if browser context exists
        context = await profile_manager.camoufox_manager.get_browser_context(browser_profile_id)
        if context:
            print(f"   ✅ Browser context found for {browser_profile_id}")
            pages = context.pages
            print(f"   📄 Pages in context: {len(pages)}")
            if pages:
                page = pages[0]
                print(f"   📍 Current URL: {page.url}")
        else:
            print(f"   ❌ No browser context found for {browser_profile_id}")
            
    except Exception as e:
        print(f"❌ Browser check failed: {e}")
    
    # Test 5: Test capture browser data
    print("\n5️⃣ Testing capture browser data...")
    try:
        capture_result = await profile_manager.capture_browser_data(browser_profile_id)
        
        print(f"   📊 Capture result:")
        print(f"   ✅ Success: {capture_result.get('success')}")
        print(f"   📝 Message: {capture_result.get('message')}")
        
        if capture_result.get('success') and capture_result.get('data'):
            data = capture_result['data']
            print(f"   📊 Captured data summary:")
            print(f"      💾 localStorage: {len(data.get('localStorage', []))} items")
            print(f"      🍪 Cookies: {len(data.get('cookies', []))} items")
            print(f"      📄 Current page: {data.get('current_page', {}).get('title', 'N/A')}")
            print(f"      🔗 URL: {data.get('current_page', {}).get('url', 'N/A')}")
            print(f"      🕒 Captured at: {data.get('captured_at', 'N/A')}")
            
            # Test 6: Test save captured data
            print("\n6️⃣ Testing save captured data...")
            try:
                from app.services.profile_data_manager import ProfileDataManager
                profile_data_manager = ProfileDataManager()
                
                save_result = await profile_data_manager.save_profile_data(str(profile.id), data)
                
                if save_result['success']:
                    print(f"   ✅ Data saved successfully!")
                    print(f"      📁 Profile path: {save_result['profile_path']}")
                    print(f"      📄 Saved files: {', '.join(save_result['saved_files'])}")
                    print(f"      📊 Data sizes: {save_result['data_size']}")
                else:
                    print(f"   ❌ Data save failed: {save_result.get('message')}")
                    
            except Exception as save_error:
                print(f"   ❌ Save test failed: {save_error}")
            
        else:
            print(f"   ❌ No data captured or capture failed")
            
    except Exception as e:
        print(f"❌ Capture test failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 7: Clean up - close browser
    print("\n7️⃣ Cleaning up...")
    try:
        close_result = await profile_manager.close_browser(browser_profile_id)
        print(f"   ✅ Browser closed: {close_result.get('message')}")
    except Exception as e:
        print(f"   ⚠️ Browser close warning: {e}")
    
    print("\n🎯 Capture Debug Test Completed!")

if __name__ == "__main__":
    asyncio.run(test_capture_debug())
