#!/usr/bin/env python3
"""
Test script to verify create and delete profile workflow
"""

import asyncio
import sys
import json
import requests
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import AsyncSessionLocal
from app.models.profile import Profile
from sqlalchemy import select

async def test_create_delete_workflow():
    """Test complete create and delete profile workflow"""
    
    print("🧪 Testing Create and Delete Profile Workflow...")
    
    # Generate a fresh token for testing
    import jwt
    from datetime import datetime, timedelta

    # Create a test token (same format as the auth system)
    payload = {
        "sub": 1,
        "email": "<EMAIL>",
        "role": "admin",
        "status": "active",
        "iat": int(datetime.utcnow().timestamp()),
        "exp": int((datetime.utcnow() + timedelta(hours=1)).timestamp())
    }

    # Use the same secret key as the auth system
    secret_key = "your-secret-key-here"  # This should match your auth system
    test_token = jwt.encode(payload, secret_key, algorithm="HS256")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {test_token}"
    }
    
    # Test 1: Check current profiles in database
    print("\n1️⃣ Checking current profiles in database...")
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Profile).order_by(Profile.id))
        profiles = result.scalars().all()
        
        print(f"   📊 Current profiles in database: {len(profiles)}")
        for p in profiles:
            print(f"      - Profile {p.id}: {p.name}")
    
    # Test 2: Create a new profile via FastAPI
    print("\n2️⃣ Creating new profile via FastAPI...")
    
    try:
        create_url = "http://localhost:8000/api/profiles/"
        create_data = {
            "name": f"test_workflow_profile_{len(profiles) + 1}",
            "proxy_config": {
                "type": "no_proxy",
                "host": "",
                "port": None,
                "username": "",
                "password": ""
            }
        }
        
        print(f"   🔗 Making POST request to: {create_url}")
        print(f"   📊 Request data: {json.dumps(create_data, indent=2)}")
        
        create_response = requests.post(create_url, headers=headers, json=create_data, timeout=30)
        
        print(f"   📊 Response status: {create_response.status_code}")
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            new_profile_id = create_result.get('id')
            print(f"   ✅ Profile created successfully!")
            print(f"   📝 Profile ID: {new_profile_id}")
            print(f"   📝 Profile name: {create_result.get('name')}")
            print(f"   📝 Profile path: {create_result.get('profile_path')}")
            
        else:
            print(f"   ❌ Profile creation failed: {create_response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ Profile creation error: {e}")
        return
    
    # Test 3: Verify profile exists in database
    print("\n3️⃣ Verifying profile exists in database...")
    
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Profile).where(Profile.id == new_profile_id))
        created_profile = result.scalar_one_or_none()
        
        if created_profile:
            print(f"   ✅ Profile {new_profile_id} found in database")
            print(f"      Name: {created_profile.name}")
            print(f"      Path: {created_profile.profile_path}")
            print(f"      Status: {created_profile.status}")
        else:
            print(f"   ❌ Profile {new_profile_id} NOT found in database")
            return
    
    # Test 4: List all profiles via API
    print("\n4️⃣ Listing all profiles via API...")
    
    try:
        list_url = "http://localhost:8000/api/profiles/"
        list_response = requests.get(list_url, headers=headers, timeout=10)
        
        if list_response.status_code == 200:
            profiles_list = list_response.json()
            print(f"   ✅ API returned {len(profiles_list)} profiles")
            
            # Find our created profile
            our_profile = next((p for p in profiles_list if p['id'] == new_profile_id), None)
            if our_profile:
                print(f"   ✅ Our profile found in API response: {our_profile['name']}")
            else:
                print(f"   ❌ Our profile NOT found in API response")
                
        else:
            print(f"   ❌ Failed to list profiles: {list_response.text}")
            
    except Exception as e:
        print(f"   ❌ List profiles error: {e}")
    
    # Test 5: Delete the profile
    print("\n5️⃣ Deleting the profile...")
    
    try:
        delete_url = f"http://localhost:8000/api/profiles/{new_profile_id}"
        print(f"   🔗 Making DELETE request to: {delete_url}")
        
        delete_response = requests.delete(delete_url, headers=headers, timeout=30)
        
        print(f"   📊 Delete response status: {delete_response.status_code}")
        
        if delete_response.status_code == 200:
            delete_result = delete_response.json()
            print(f"   ✅ Profile deleted successfully!")
            print(f"   📝 Message: {delete_result.get('message')}")
            
        else:
            print(f"   ❌ Profile deletion failed: {delete_response.text}")
            
    except Exception as e:
        print(f"   ❌ Profile deletion error: {e}")
    
    # Test 6: Verify profile is deleted from database
    print("\n6️⃣ Verifying profile is deleted from database...")
    
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Profile).where(Profile.id == new_profile_id))
        deleted_profile = result.scalar_one_or_none()
        
        if deleted_profile:
            print(f"   ❌ Profile {new_profile_id} still exists in database")
        else:
            print(f"   ✅ Profile {new_profile_id} successfully deleted from database")
    
    # Test 7: Final profile count
    print("\n7️⃣ Final profile count...")
    
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Profile).order_by(Profile.id))
        final_profiles = result.scalars().all()
        
        print(f"   📊 Final profiles in database: {len(final_profiles)}")
        for p in final_profiles:
            print(f"      - Profile {p.id}: {p.name}")
    
    print("\n🎯 Create and Delete Workflow Test Completed!")
    print("\n📋 Summary:")
    print("   ✅ Profile creation via FastAPI tested")
    print("   ✅ Database verification tested")
    print("   ✅ API listing tested")
    print("   ✅ Profile deletion via FastAPI tested")
    print("   ✅ Database cleanup verified")
    
    print("\n💡 Frontend should now work properly:")
    print("   1. Create profile via ProfileForm.js (now uses FastAPI)")
    print("   2. Delete profile via ProfileList.js (uses FastAPI)")
    print("   3. Both operations use same database")

if __name__ == "__main__":
    asyncio.run(test_create_delete_workflow())
