#!/usr/bin/env python3
"""
Test script to debug profile 3 specifically
"""

import asyncio
import sys
import json
import requests
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import AsyncSessionLocal
from app.models.profile import Profile
from app.services.profile_manager import AntidetectProfileManager
from sqlalchemy import select

async def test_profile_3_debug():
    """Test profile 3 specifically with debug logging"""
    
    print("🧪 Testing Profile 3 Debug...")
    
    # Test 1: Check if profile 3 exists
    print("\n1️⃣ Checking if profile 3 exists...")
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Profile).where(Profile.id == 3))
        profile = result.scalar_one_or_none()
        
        if profile:
            print(f"✅ Profile 3 exists: {profile.name}")
            print(f"   📁 Profile path: {profile.profile_path}")
        else:
            print("❌ Profile 3 not found")
            return
    
    # Test 2: Initialize ProfileManager and check active browsers
    print("\n2️⃣ Checking current active browsers...")
    try:
        profile_manager = AntidetectProfileManager()
        
        # Check active browsers
        camoufox_browsers = list(profile_manager.camoufox_manager.active_browsers.keys())
        legacy_browsers = list(profile_manager.active_browsers.keys())
        
        print(f"   🔍 CamoufoxBrowserManager active browsers: {camoufox_browsers}")
        print(f"   🔍 ProfileManager active browsers: {legacy_browsers}")
        
        # Get expected browser profile ID
        import os
        expected_browser_id = os.path.basename(profile.profile_path)
        print(f"   🔍 Expected browser profile ID: {expected_browser_id}")
        
        # Check if browser is running for profile 3
        if expected_browser_id in camoufox_browsers:
            print(f"   ✅ Browser is running for profile 3: {expected_browser_id}")
        else:
            print(f"   ❌ Browser NOT running for profile 3")
            print(f"   💡 Need to launch browser first")
            
            # Test 3: Launch browser for profile 3
            print("\n3️⃣ Launching browser for profile 3...")
            try:
                launch_result = await profile_manager.launch_browser(
                    profile_path=profile.profile_path,
                    proxy_config=None,
                    headless=False,
                    browser_data=None
                )
                
                if launch_result.get('success'):
                    print(f"   ✅ Browser launched successfully")
                    
                    # Update browser lists
                    camoufox_browsers = list(profile_manager.camoufox_manager.active_browsers.keys())
                    print(f"   🔍 Updated active browsers: {camoufox_browsers}")
                    
                    # Wait a moment for browser to be ready
                    await asyncio.sleep(2)
                else:
                    print(f"   ❌ Browser launch failed: {launch_result.get('message')}")
                    return
                    
            except Exception as e:
                print(f"   ❌ Browser launch error: {e}")
                return
    
    except Exception as e:
        print(f"❌ ProfileManager initialization failed: {e}")
        return
    
    # Test 4: Test API capture endpoint
    print("\n4️⃣ Testing API capture endpoint...")
    try:
        url = "http://localhost:8000/api/profiles/3/capture-browser-data"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.FjrLU1ZPH-BtKlqWvash_4OP6Iz1OkRWZ6v9ArwN8nw"
        }
        
        print(f"   🔗 Making POST request to: {url}")
        response = requests.post(url, headers=headers, json={}, timeout=30)
        
        print(f"   📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API call successful!")
            print(f"   📊 Response data:")
            print(f"      Success: {data.get('success')}")
            print(f"      Message: {data.get('message')}")
            
            if data.get('success') and data.get('data'):
                browser_data = data['data']
                print(f"      📊 Captured data summary:")
                print(f"         💾 localStorage: {len(browser_data.get('localStorage', []))} items")
                print(f"         🍪 Cookies: {len(browser_data.get('cookies', []))} items")
                print(f"         📄 Current page: {browser_data.get('current_page', {}).get('title', 'N/A')}")
                print(f"         🔗 URL: {browser_data.get('current_page', {}).get('url', 'N/A')}")
                
                # Test 5: Test save API with captured data
                print("\n5️⃣ Testing save API with captured data...")
                
                save_url = "http://localhost:8000/api/profiles/3/save-browser-data"
                save_response = requests.post(save_url, headers=headers, json=browser_data, timeout=30)
                
                print(f"   📊 Save response status: {save_response.status_code}")
                
                if save_response.status_code == 200:
                    save_data = save_response.json()
                    print(f"   ✅ Save API successful!")
                    print(f"      Success: {save_data.get('success')}")
                    print(f"      Message: {save_data.get('message')}")
                    print(f"      Saved files: {save_data.get('saved_files')}")
                    print(f"      Profile path: {save_data.get('profile_path')}")
                    print(f"      Data size: {save_data.get('data_size')}")
                else:
                    print(f"   ❌ Save API failed: {save_response.text}")
            else:
                print(f"   ⚠️ No browser data captured")
                
        else:
            print(f"   ❌ API call failed: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Request failed: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    # Test 6: Clean up - close browser
    print("\n6️⃣ Cleaning up...")
    try:
        import os
        browser_profile_id = os.path.basename(profile.profile_path)
        close_result = await profile_manager.close_browser(browser_profile_id)
        print(f"   ✅ Browser closed: {close_result.get('message')}")
    except Exception as e:
        print(f"   ⚠️ Browser close warning: {e}")
    
    print("\n🎯 Profile 3 Debug Test Completed!")

if __name__ == "__main__":
    asyncio.run(test_profile_3_debug())
