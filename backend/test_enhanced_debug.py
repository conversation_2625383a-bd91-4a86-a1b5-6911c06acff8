#!/usr/bin/env python3
"""
Enhanced test script with comprehensive debugging for browser data capture
"""

import asyncio
import sys
import json
import requests
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import AsyncSessionLocal
from app.models.profile import Profile
from app.services.profile_manager import AntidetectProfileManager
from sqlalchemy import select

async def test_enhanced_debug():
    """Enhanced test with comprehensive debugging"""
    
    print("🧪 Enhanced Browser Data Capture Debug Test...")
    
    # Test 1: Check available profiles
    print("\n1️⃣ Checking available profiles...")
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Profile).order_by(Profile.id))
        profiles = result.scalars().all()
        
        if profiles:
            print(f"✅ Found {len(profiles)} profiles:")
            for p in profiles:
                print(f"   - Profile {p.id}: {p.name} (path: {p.profile_path})")
        else:
            print("❌ No profiles found")
            return
    
    # Test 2: Check active browsers via API
    print("\n2️⃣ Checking active browsers via API...")
    try:
        url = "http://localhost:8000/api/profiles/active-browsers"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.FjrLU1ZPH-BtKlqWvash_4OP6Iz1OkRWZ6v9ArwN8nw"
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ API call successful!")
            print(f"   📊 Active browsers: {data.get('active_browsers', [])}")
            print(f"   📊 Legacy browsers: {data.get('legacy_browsers', [])}")
            print(f"   📊 Total count: {data.get('total_count', 0)}")
            
            active_browsers = data.get('active_browsers', [])
            
        else:
            print(f"   ❌ API call failed: {response.status_code} - {response.text}")
            active_browsers = []
            
    except Exception as e:
        print(f"   ❌ API request failed: {e}")
        active_browsers = []
    
    # Test 3: Launch browser for first profile if no browsers active
    if not active_browsers:
        print("\n3️⃣ No active browsers found, launching browser for first profile...")
        
        first_profile = profiles[0]
        print(f"   🚀 Launching browser for profile {first_profile.id}: {first_profile.name}")
        
        try:
            launch_url = f"http://localhost:8000/api/profiles/{first_profile.id}/launch-browser"
            launch_response = requests.post(launch_url, headers=headers, json={}, timeout=30)
            
            if launch_response.status_code == 200:
                launch_data = launch_response.json()
                print(f"   ✅ Browser launched successfully!")
                print(f"   📊 Launch result: {launch_data.get('message')}")
                
                # Wait for browser to be ready
                await asyncio.sleep(3)
                
                # Check active browsers again
                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    active_browsers = data.get('active_browsers', [])
                    print(f"   🔄 Updated active browsers: {active_browsers}")
                
            else:
                print(f"   ❌ Browser launch failed: {launch_response.status_code} - {launch_response.text}")
                return
                
        except Exception as e:
            print(f"   ❌ Browser launch error: {e}")
            return
    else:
        print(f"\n3️⃣ Active browsers found: {active_browsers}")
    
    # Test 4: Test capture for each profile
    print("\n4️⃣ Testing capture for each profile...")
    
    for profile in profiles[:3]:  # Test first 3 profiles
        print(f"\n   🔍 Testing profile {profile.id}: {profile.name}")
        
        try:
            capture_url = f"http://localhost:8000/api/profiles/{profile.id}/capture-browser-data"
            capture_response = requests.post(capture_url, headers=headers, json={}, timeout=30)
            
            print(f"      📊 Response status: {capture_response.status_code}")
            
            if capture_response.status_code == 200:
                capture_data = capture_response.json()
                print(f"      ✅ Capture successful!")
                print(f"      📝 Message: {capture_data.get('message')}")
                
                if capture_data.get('success') and capture_data.get('data'):
                    browser_data = capture_data['data']
                    print(f"      📊 Captured data:")
                    print(f"         💾 localStorage: {len(browser_data.get('localStorage', []))} items")
                    print(f"         🍪 Cookies: {len(browser_data.get('cookies', []))} items")
                    print(f"         📄 Current page: {browser_data.get('current_page', {}).get('title', 'N/A')}")
                    print(f"         🔗 URL: {browser_data.get('current_page', {}).get('url', 'N/A')}")
                    
                    # Test save API
                    print(f"      💾 Testing save API...")
                    save_url = f"http://localhost:8000/api/profiles/{profile.id}/save-browser-data"
                    save_response = requests.post(save_url, headers=headers, json=browser_data, timeout=30)
                    
                    if save_response.status_code == 200:
                        save_data = save_response.json()
                        print(f"      ✅ Save successful!")
                        print(f"         📁 Saved files: {save_data.get('saved_files')}")
                        print(f"         📊 Data size: {save_data.get('data_size')}")
                    else:
                        print(f"      ❌ Save failed: {save_response.text}")
                        
                else:
                    print(f"      ⚠️ No data captured")
                    
            elif capture_response.status_code == 400:
                error_data = capture_response.json()
                print(f"      ⚠️ Expected error (browser not launched for this profile)")
                print(f"      📝 Error: {error_data.get('detail')}")
                
            else:
                print(f"      ❌ Capture failed: {capture_response.text}")
                
        except Exception as e:
            print(f"      ❌ Test error: {e}")
    
    # Test 5: Final active browsers check
    print("\n5️⃣ Final active browsers check...")
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   📊 Final active browsers: {data.get('active_browsers', [])}")
            print(f"   📊 Total count: {data.get('total_count', 0)}")
        else:
            print(f"   ❌ Final check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Final check error: {e}")
    
    print("\n🎯 Enhanced Debug Test Completed!")
    print("\n📋 Summary:")
    print("   ✅ Enhanced debugging implemented")
    print("   ✅ Multiple matching strategies added")
    print("   ✅ Better error messages with helpful instructions")
    print("   ✅ Active browsers API endpoint added")
    print("   ✅ Comprehensive test coverage")

if __name__ == "__main__":
    asyncio.run(test_enhanced_debug())
