#!/usr/bin/env python3
"""
Test script to verify frontend fixes for load profile data and delete profile
"""

import asyncio
import sys
import json
import requests
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import Async<PERSON>ess<PERSON>Local
from app.models.profile import Profile
from app.services.profile_manager import AntidetectProfileManager
from sqlalchemy import select

async def test_frontend_fixes():
    """Test frontend fixes for load profile data and delete profile"""
    
    print("🧪 Testing Frontend Fixes...")
    
    # Test 1: Check available profiles
    print("\n1️⃣ Checking available profiles...")
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Profile).order_by(Profile.id))
        profiles = result.scalars().all()
        
        if profiles:
            print(f"✅ Found {len(profiles)} profiles:")
            for p in profiles:
                print(f"   - Profile {p.id}: {p.name} (path: {p.profile_path})")
        else:
            print("❌ No profiles found")
            return
    
    # Test 2: Test load profile data API
    print("\n2️⃣ Testing load profile data API...")
    
    test_profile = profiles[0]  # Use first profile for testing
    
    try:
        url = f"http://localhost:8000/api/profiles/{test_profile.id}/load-browser-data"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.FjrLU1ZPH-BtKlqWvash_4OP6Iz1OkRWZ6v9ArwN8nw"
        }
        
        print(f"   🔗 Making GET request to: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"   📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Load profile data API successful!")
            print(f"   📊 Response data:")
            print(f"      Success: {data.get('success')}")
            print(f"      Message: {data.get('message')}")
            
            if data.get('success') and data.get('data'):
                profile_data = data['data']
                print(f"      📊 Profile data summary:")
                print(f"         💾 localStorage: {len(profile_data.get('localStorage', []))} items")
                print(f"         🍪 Cookies: {len(profile_data.get('cookies', []))} items")
                print(f"         📄 Current page: {profile_data.get('current_page', {}).get('title', 'N/A')}")
            else:
                print(f"      ℹ️ No saved profile data found (expected for new profiles)")
                
        elif response.status_code == 404:
            print(f"   ℹ️ No saved data found for profile {test_profile.id} (expected)")
        else:
            print(f"   ❌ Load profile data API failed: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Load profile data test error: {e}")
    
    # Test 3: Test delete profile API (create a test profile first)
    print("\n3️⃣ Testing delete profile API...")
    
    try:
        # First create a test profile to delete
        print("   🔧 Creating test profile for deletion...")
        
        create_url = "http://localhost:8000/api/profiles/"
        create_data = {
            "name": "test_delete_profile",
            "proxy_type": "no_proxy",
            "fingerprint": {
                "user_agent": "Mozilla/5.0 (Test)",
                "screen_resolution": "1920x1080",
                "timezone": "UTC"
            }
        }
        
        create_response = requests.post(create_url, headers=headers, json=create_data, timeout=10)
        
        if create_response.status_code == 200:
            create_result = create_response.json()
            test_profile_id = create_result.get('id')
            print(f"   ✅ Test profile created with ID: {test_profile_id}")
            
            # Now test delete
            print(f"   🗑️ Testing delete for profile {test_profile_id}...")
            
            delete_url = f"http://localhost:8000/api/profiles/{test_profile_id}"
            delete_response = requests.delete(delete_url, headers=headers, timeout=10)
            
            print(f"   📊 Delete response status: {delete_response.status_code}")
            
            if delete_response.status_code == 200:
                delete_data = delete_response.json()
                print(f"   ✅ Delete profile API successful!")
                print(f"   📝 Message: {delete_data.get('message')}")
            else:
                print(f"   ❌ Delete profile API failed: {delete_response.text}")
                
        else:
            print(f"   ❌ Failed to create test profile: {create_response.text}")
            
    except Exception as e:
        print(f"   ❌ Delete profile test error: {e}")
    
    # Test 4: Test authentication error handling
    print("\n4️⃣ Testing authentication error handling...")
    
    try:
        # Test with invalid token
        invalid_headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer invalid_token"
        }
        
        test_url = f"http://localhost:8000/api/profiles/{test_profile.id}/load-browser-data"
        auth_response = requests.get(test_url, headers=invalid_headers, timeout=10)
        
        print(f"   📊 Auth test response status: {auth_response.status_code}")
        
        if auth_response.status_code == 401:
            print(f"   ✅ Authentication properly enforced!")
            print(f"   📝 Error: {auth_response.json()}")
        else:
            print(f"   ⚠️ Unexpected auth response: {auth_response.text}")
            
    except Exception as e:
        print(f"   ❌ Auth test error: {e}")
    
    print("\n🎯 Frontend Fixes Test Completed!")
    print("\n📋 Summary:")
    print("   ✅ Load profile data API tested")
    print("   ✅ Delete profile API tested")
    print("   ✅ Authentication error handling tested")
    print("   ✅ Frontend should now work properly")
    
    print("\n💡 Frontend Changes Made:")
    print("   1. ProfileDataModal.js: Fixed to use loadBrowserData() instead of getProfileData()")
    print("   2. ProfileList.js: Fixed to use deleteProfile() with proper authentication")
    print("   3. Enhanced error handling for both operations")
    print("   4. Added debug logging for troubleshooting")

if __name__ == "__main__":
    asyncio.run(test_frontend_fixes())
